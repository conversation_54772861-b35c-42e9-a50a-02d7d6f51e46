import{K as p,T as c,o as u,c as _,a,u as e,w as r,F as f,Z as g,b as o,h as v,g as b}from"./app-1e83883d.js";import{_ as x,b as h}from"./AdminLayout-d8381eea.js";import{_ as m}from"./InputError-b46459b8.js";import{_ as l}from"./InputLabel-e29df6ea.js";import{P as y}from"./PrimaryButton-e7d804f6.js";import{_ as V}from"./TextInput-ea9e10ee.js";import{_ as $}from"./TextArea-d6832033.js";import"./_plugin-vue_export-helper-c27b6911.js";const w={class:"animate-top bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},T=o("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Tag",-1),B={class:"border-b border-gray-900/10 pb-12"},C={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},E={class:"sm:col-span-6"},N={class:"sm:col-span-6"},j={class:"flex mt-6 items-center justify-between"},k={class:"ml-auto flex items-center justify-end gap-x-6"},D=o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),A={__name:"Edit",props:["data"],setup(F){const n=p().props.data,s=c({id:n.id,name:n.name,description:n.description});return(d,t)=>(u(),_(f,null,[a(e(g),{title:"Edit Tag"}),a(x,null,{default:r(()=>[o("div",w,[T,o("form",{onSubmit:t[4]||(t[4]=v(i=>e(s).patch(d.route("email-tag.update")),["prevent"]))},[o("div",B,[o("div",C,[o("div",E,[a(l,{for:"name",value:"Tag Name"}),a(V,{id:"name",type:"text",modelValue:e(s).name,"onUpdate:modelValue":t[0]||(t[0]=i=>e(s).name=i),autocomplete:"name",onChange:t[1]||(t[1]=i=>e(s).validate("name"))},null,8,["modelValue"]),a(m,{class:"",message:e(s).errors.name},null,8,["message"])]),o("div",N,[a(l,{for:"description",value:"Description"}),a($,{id:"description",type:"text",rows:2,modelValue:e(s).description,"onUpdate:modelValue":t[2]||(t[2]=i=>e(s).description=i),onChange:t[3]||(t[3]=i=>e(s).validate("description"))},null,8,["modelValue"]),a(m,{class:"",message:e(s).errors.description},null,8,["message"])])])]),o("div",j,[o("div",k,[a(h,{href:d.route("email-tag.index")},{svg:r(()=>[D]),_:1},8,["href"]),a(y,{disabled:e(s).processing},{default:r(()=>[b("Save")]),_:1},8,["disabled"])])])],32)])]),_:1})],64))}};export{A as default};
