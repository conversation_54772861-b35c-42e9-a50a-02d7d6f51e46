<script setup>
import {ref, onMounted} from 'vue';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import NavLink from '@/Components/NavLink.vue';
import SideMenu from '@/Components/SideMenu.vue';
import ToastNotificationVue from '@/Components/ToastNotification.vue'
import ToastNotificationSuccessVue from '@/Components/ToastNotificationSuccess.vue'
import ToastNotificationErrorVue from '@/Components/ToastNotificationError.vue'
import ToastNotificationWarningVue from '@/Components/ToastNotificationWarning.vue'
import ActionLink from '@/Components/ActionLink.vue';
import {Link, useForm} from '@inertiajs/vue3';

const logoSrc = ref('/uploads/companyprofile/defaultimg.png');
const form = useForm({});
const updateLogoSrc = (value) => {
    logoSrc.value = value;
};

const logo = ref('/mototive.png');

onMounted(async () => {
    // try {
    //     const response = await fetch('/api/logo');
    //     if (response.ok) {
    //         const data = await response.json();
    //         if (data.logoUrl) {
    //             updateLogoSrc('/uploads/companyprofile/' + data.logoUrl);
    //         } else {
    //             updateLogoSrc('/uploads/companyprofile/defaultimg.png');
    //         }
    //     }
    // } catch (error) {
    //     console.error('Error fetching logo:', error);
    // }
});

const hasPermission = (permission) => {
//    return this.$store.state.user.permissions.includes(permission);
};

</script>

<template>
    <div>
        <!-- Static sidebar for desktop -->
        <div class="lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col border-t border-r bg-gray-900">
            <div class="bg-gray-900 flex h-16 px-6 items-center px-10 shrink-0 w-full mt-2">
                <img :src="logo" alt="LOGO">
                <!-- <img alt="Your Company" src="https://tailwindcss.com/plus-assets/img/logos/mark.svg?color=indigo&amp;shade=500" class="h-10"> -->
            </div>
            <!-- Sidebar component, swap this element with another sidebar if you like -->
            <div class="flex grow flex-col gap-y-2 overflow-y-auto bg-gray-900 mt-2">
                <nav class="flex flex-1 flex-col px-6">
                    <ul role="list" class="flex flex-1 flex-col gap-y-7">
                        <li>
                            <ul role="list" class="-mx-2 space-y-1">
                                <li>
                                    <SideMenu  :href="route('dashboard')"
                                              :active="route().current('dashboard')">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                 :class="route().current('dashboard') ? 'text-white' : 'text-gray-500 group-hover:text-white'"
                                                 fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"/>
                                            </svg>
                                        </template>
                                        <template #name>Dashboard</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu  :href="route('users.index')"
                                              :active="(route().current('users.index') || route().current('users.create') || route().current('users.edit') )">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                 :class="(route().current('users.index') || route().current('users.create') || route().current('users.edit')) ? 'text-white' : 'text-gray-500 group-hover:text-white'"
                                                 fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"/>
                                            </svg>
                                        </template>
                                        <template #name>Users</template>
                                    </SideMenu>
                                </li>

                                <li>
                                    <SideMenu  :href="route('prospects.index')"
                                              :active="(route().current('prospects.index') || route().current('prospects.create') || route().current('prospects.edit') || route().current('prospects.show') )">
                                              <template #svg>
                                                    <svg class="h-6 w-6 shrink-0"
                                                        :class="(route().current('prospects.index') || route().current('prospects.create') || route().current('prospects.edit') || route().current('prospects.show')) ? 'text-white' : 'text-gray-500 group-hover:text-white'"
                                                        fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"/>
                                                    </svg>
                                                </template>
                                        <template #name>Prospects</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu  :href="route('portfolios.index')"
                                              :active="(route().current('portfolios.index') || route().current('portfolios.create') || route().current('portfolios.edit') || route().current('portfolios.show') )">
                                              <template #svg>
                                                    <svg class="h-6 w-6 shrink-0"
                                                        :class="(route().current('portfolios.index') || route().current('portfolios.create') || route().current('portfolios.edit') || route().current('portfolios.show')) ? 'text-white' : 'text-gray-500 group-hover:text-white'"
                                                        fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"/>
                                                    </svg>
                                                </template>
                                        <template #name>Portfolio</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu  :href="route('leads.index')"
                                              :active="(route().current('leads.index') || route().current('leads.create') || route().current('leads.edit') )">
                                              <template #svg>
                                                    <svg class="h-6 w-6 shrink-0"
                                                        :class="(route().current('leads.index') || route().current('leads.create') || route().current('leads.edit')) ? 'text-white' : 'text-gray-500 group-hover:text-white'"
                                                        fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M15.75 9A3.75 3.75 0 1 1 12 5.25 3.75 3.75 0 0 1 15.75 9zM20.25 15.75a3 3 0 1 0-6 0m6 0A3.75 3.75 0 0 1 12 18m8.25-2.25v1.5A5.25 5.25 0 0 1 15 21m-3-15.75A3.75 3.75 0 1 1 5.25 9M9 15.75a3 3 0 1 0-6 0m6 0A3.75 3.75 0 0 1 3.75 18m5.25-2.25v1.5A5.25 5.25 0 0 1 3 21"/>
                                                    </svg>
                                                </template>
                                        <template #name>Leads</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu  :href="route('email-sequence.index')"
                                              :active="(route().current('email-sequence.index') || route().current('email-sequence.create') || route().current('email-sequence.edit') || route().current('sequence-step.show') || route().current('sequence-step.create') || route().current('sequence-step.edit') )">
                                              <template #svg>
                                                    <svg class="h-6 w-6 shrink-0"
                                                        :class="(route().current('email-sequence.index') || route().current('email-sequence.create') || route().current('email-sequence.edit')) || route().current('sequence-step.show') || route().current('sequence-step.create') || route().current('sequence-step.edit') ? 'text-white' : 'text-gray-500 group-hover:text-white'"
                                                        fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12H8m8-4H8m8 8H8M5 5v14l-2-2m2 2 2-2M16 5l-4 4-4-4m8 0H8m8 0-4 4-4-4m0 8h8m-8-4h8m-8 4h8"/>
                                                    </svg>
                                                </template>
                                        <template #name>Sequences</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu  :href="route('smtp.index')"
                                              :active="(route().current('smtp.index') || route().current('smtp.create') || route().current('smtp.edit') )">
                                              <template #svg>
                                                    <svg class="h-6 w-6 shrink-0"
                                                        :class="(route().current('smtp.index') || route().current('smtp.create') || route().current('smtp.edit')) ? 'text-white' : 'text-gray-500 group-hover:text-white'"
                                                        fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16.5 3h-9A2.5 2.5 0 005 5.5v13A2.5 2.5 0 007.5 21h9a2.5 2.5 0 002.5-2.5v-13A2.5 2.5 0 0016.5 3zM5 8l7 5 7-5"/>
                                                    </svg>
                                                </template>
                                        <template #name>SMTP</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu  :href="route('email-tag.index')"
                                              :active="(route().current('email-tag.index') || route().current('email-tag.create') || route().current('email-tag.edit') )">
                                              <template #svg>
                                                <svg class="h-6 w-6 shrink-0">
                                                <path d="M2 10V4a2 2 0 0 1 2-2h6l10 10a2 2 0 0 1 0 3l-6 6a2 2 0 0 1-3 0L2 12V10z" stroke="currentColor" stroke-width="2" fill="none"/>
                                                    <circle cx="7" cy="7" r="2" fill="currentColor"/>
                                                    <path d="M15 12l 5M18 9l4" stroke="currentColor" stroke-width="2" fill="none"/>
                                                </svg>
                                                </template>
                                        <template #name>Email Tags</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu  :href="route('sent-email.index')"
                                              :active="(route().current('sent-email.index') || route().current('sent-email.edit'))">
                                              <template #svg>
                                                <svg class="h-6 w-6 shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M6 18V6L12 12L18 6V18" />
                                                </svg>
                                                </template>
                                        <template #name>Sent Emails</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu  :href="route('failed-emails.index')"
                                              :active="(route().current('failed-emails.index') || route().current('failed-emails.edit'))">
                                              <template #svg>
                                                <svg class="h-6 w-6 shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                                </svg>
                                                </template>
                                        <template #name>Failed Emails</template>
                                    </SideMenu>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
            <div class="mt-auto px-4 mt-1">
                <SideMenu :href="route('setting')" :active="(route().current('setting') ||  route().current('roles.permission') ||
                        route().current('roles.index') || route().current('roles.create') || route().current('roles.edit'))">
                    <template #svg>
                        <svg class="h-6 w-6 shrink-0 text-gray-500" :class="(route().current('setting') ||
                                    route().current('roles.index') || route().current('roles.create') || route().current('roles.edit')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                    </template>
                    <template #name>Settings</template>
                </SideMenu>
            </div>
        </div>

        <div class="lg:pl-72 border-t">
            <div class="sticky top-0 z-20 flex h-16 shrink-0 items-center gap-x-4 bg-white px-4 shadow sm:gap-x-6 sm:px-6 lg:px-8">
                <button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden">
                    <span class="sr-only">Open sidebar</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"/>
                    </svg>
                </button>

                <!-- Separator -->
                <div class="h-6 w-px bg-gray-200 lg:hidden" aria-hidden="true"></div>

                <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6 justify-between">
                    <div class="flex items-center">
                        <Dropdown align="left" width="48">
                            <template #trigger>
                                <div class="flex w-32">
                                    <a class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" href="#"> Add New +</a>
                                </div>
                            </template>
                            <template #content>
                                <ActionLink :href="route('users.create')">
                                    <template #svg></template>
                                    <template #text>
                                        <span class="text-sm text-gray-700 leading-6">Add New User</span>
                                    </template>
                                </ActionLink>

                            </template>
                        </Dropdown>
                    </div>
                    <div class="flex items-center gap-x-4 lg:gap-x-6">
                        <button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500">
                            <span class="sr-only">View Notifications</span>
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                 stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                      d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"/>
                            </svg>
                        </button>

                        <!-- Notifications -->
                        <!--          <Notification />-->

                        <!-- Separator -->
                        <div class="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200" aria-hidden="true"></div>

                        <!-- Profile dropdown -->
                        <div class="relative">
                            <Dropdown align="right" width="48">
                                <template #trigger>
                                    <button type="button" class="-m-1.5 flex items-center p-1.5" id="user-menu-button"
                                            aria-expanded="false" aria-haspopup="true">
                                        <span class="sr-only">Open User Menu</span>
                                        <img class="h-8 w-8 rounded-full bg-gray-50"
                                             src="https://img.freepik.com/premium-photo/avatar-resourcing-company_1254967-6696.jpg?size=626&ext=jpg&ga=GA1.1.*********.1729255085&semt=ais_hybrid"
                                             alt="">
                                        <span class="hidden lg:flex lg:items-center">
                                <span class="ml-4 text-sm font-semibold leading-6 text-gray-900" aria-hidden="true">{{ $page.props.auth.user.name }}</span>
                                <svg class="ml-2 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"/>
                                </svg>
                            </span>
                                    </button>
                                </template>
                                <template #content>
                                    <ActionLink :href="route('profile.edit')" as="button">
                                        <template #svg></template>
                                        <template #text>
                                            <span class="text-sm text-gray-700 leading-5">Your Profile</span>
                                        </template>
                                    </ActionLink>
                                    <ActionLink :href="route('logout')" method="post" as="button">
                                        <template #svg></template>
                                        <template #text>
                                            <span class="text-sm text-gray-700 leading-5">Sign Out</span>
                                        </template>
                                    </ActionLink>
                                </template>
                            </Dropdown>
                        </div>
                    </div>


                </div>
            </div>

            <div v-if="$page.props.flash.message">
                <ToastNotificationVue :message="$page.props.flash.message"/>
            </div>

            <div v-if="$page.props.flash.success">
                <ToastNotificationSuccessVue :message="$page.props.flash.success"/>
            </div>

            <!-- {{ $page.props.flash.error }} -->
            <div v-if="$page.props.flash.error">
                <ToastNotificationErrorVue :message="$page.props.flash.error"/>
            </div>

            <div v-if="$page.props.flash.warning">
                <ToastNotificationWarningVue :message="$page.props.flash.warning"/>
            </div>

            <main class="py-10 bg-gray-100">
                <div class="px-4 sm:px-6 lg:px-8 min-h-screen">
                    <slot/>
                </div>
            </main>
        </div>
    </div>
</template>

<style>

::-webkit-scrollbar {
    display: none;
}

html {
    scrollbar-width: none;
}

.bg-gray-900 {
    /* background: #ffffff !important;; */
}


/* .hover\:bg-indigo-500:hover {
    background: rgb(62 81 228) !important;
}

.bg-indigo-600 {
    background: rgb(50 68 210) !important;
} */

.bg-gray-100 {
    background: #e3e9f1bd !important;
}

.animate-top{
    position:relative;
    animation:animatetop 0.6s
}
@keyframes animatetop
    {from{top:-100px;opacity:0} to{top:0;opacity:1}}



</style>
