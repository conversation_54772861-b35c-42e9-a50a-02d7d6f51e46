<script setup>
import AdminLayout from "@/Layouts/AdminLayout.vue";
import { Head, Link, router } from '@inertiajs/vue3';

const props = defineProps(['portfolio']);

const deletePortfolio = () => {
    if (confirm('Are you sure you want to delete this portfolio item?')) {
        router.delete(route('portfolios.destroy', props.portfolio.id));
    }
};
</script>

<template>
    <Head title="Portfolio" />

    <AdminLayout>
        <div class="items-start">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Project Details</h1>
                <div class="flex space-x-2">
                    <!-- <Link :href="route('portfolios.edit', portfolio.id)"
                          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Edit Project
                    </Link>
                    <button @click="deletePortfolio"
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Delete Project
                    </button> -->
                    <Link :href="route('portfolios.index')"
                          class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Back to Portfolio
                    </Link>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow overflow-hidden">
                <!-- Header Section -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-start">
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">{{ portfolio.project_name }}</h2>
                            <p class="text-sm text-gray-500 mt-1">
                                Created on {{ new Date(portfolio.created_at).toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'long',
                                    day: 'numeric'
                                }) }}
                            </p>
                            <p v-if="portfolio.updated_at !== portfolio.created_at" class="text-sm text-gray-500">
                                Last updated on {{ new Date(portfolio.updated_at).toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'long',
                                    day: 'numeric'
                                }) }}
                            </p>
                        </div>
                        <div v-if="portfolio.url" class="flex-shrink-0">
                            <a :href="portfolio.url"
                               target="_blank"
                               class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                View Live Project
                            </a>
                        </div>
                    </div>
                </div>
                <!-- Content Section -->
                <div class="px-6 py-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Main Content -->
                        <div class="lg:col-span-2 space-y-6">
                            <!-- Description -->
                            <div v-if="portfolio.description">
                                <h3 class="text-sm font-semibold text-gray-900 mb-3">Description</h3>
                                <div class="max-w-none">
                                    <p class="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap">{{ portfolio.description }}</p>
                                </div>
                            </div>

                            <!-- Technologies -->
                            <div v-if="portfolio.technology">
                                <h3 class="text-sm font-semibold text-gray-900 mb-3">Technologies Used</h3>
                                <div class="max-w-none">
                                    <p class="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap">{{ portfolio.technology }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Sidebar -->
                        <div class="lg:col-span-1">
                            <div class="bg-gray-50 rounded-lg p-4 space-y-4">
                                <h3 class="text-sm font-semibold text-gray-900 mb-3">Project Details</h3>
                                <!-- Project URL -->
                                <div v-if="portfolio.url">
                                    <dt class="text-sm font-medium text-gray-500">Project URL</dt>
                                    <dd class="mt-1">
                                        <a :href="portfolio.url"
                                           target="_blank"
                                           class="text-blue-600 hover:text-blue-800 text-sm break-all">
                                            {{ portfolio.url }}
                                        </a>
                                    </dd>
                                </div>
                                <!-- Created Date -->
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        {{ new Date(portfolio.created_at).toLocaleDateString('en-US', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        }) }}
                                    </dd>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
