<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;
use App\Models\LeadSequence;
use App\Models\Leads;
use App\Models\Sequences;
use App\Models\Prospect;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $query = LeadSequence::query();

        // Apply custom date filter if both dates are present
        if ($request->startDate && $request->endDate) {
            $query->whereBetween('created_at', [
                Carbon::parse($request->startDate)->startOfDay(),
                Carbon::parse($request->endDate)->endOfDay(),
            ]);
        } elseif ($request->range && $request->range !== 'all_time') {
            // Apply quick range filters only if no custom dates
            switch ($request->range) {
                case 'today':
                    $query->whereDate('created_at', Carbon::today());
                    break;
                case 'last_week':
                    $query->whereBetween('created_at', [now()->subWeek(), now()]);
                    break;
                case 'last_month':
                    $query->whereBetween('created_at', [now()->subMonth(), now()]);
                    break;
                case 'last_quarter':
                    $query->whereBetween('created_at', [now()->subMonths(3), now()]);
                    break;
            }
        }

        $deliveredEmails = (clone $query)->where('status', 'completed')->count();
        $leadSequenceCount = (clone $query)->count();
        $leadCount = Leads::count();
        $sequenceCount = Sequences::count();

        // 📊 Dynamic chart data: grouped by month
        $chartData = LeadSequence::select(
            DB::raw("DATE_FORMAT(sent_time, '%Y-%m') as month"),
            DB::raw("COUNT(*) as sent"),
            DB::raw("SUM(CASE WHEN opened_at IS NOT NULL THEN 1 ELSE 0 END) as opened"),
            DB::raw("SUM(CASE WHEN clicked_at IS NOT NULL THEN 1 ELSE 0 END) as clicked")
        )
        ->when($request->startDate && $request->endDate, function ($q) use ($request) {
            $q->whereBetween('sent_time', [
                Carbon::parse($request->startDate)->startOfDay(),
                Carbon::parse($request->endDate)->endOfDay(),
            ]);
        })
        ->groupBy('month')
        ->orderBy('month')
        ->get();

        // 📈 Format data for Chart.js
        $labels = $chartData->pluck('month');
        $opened = $chartData->pluck('opened');
        $clicked = $chartData->pluck('clicked');

        $emailChart = [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Opened Emails',
                    'data' => $opened,
                    'borderColor' => '#10B981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.3)',
                    'fill' => true,
                    'tension' => 0.3,
                    'pointRadius' => 4,
                    'pointBackgroundColor' => '#fff',
                    'pointBorderColor' => '#10B981',
                ],
                [
                    'label' => 'Clicked Emails',
                    'data' => $clicked,
                    'borderColor' => '#3B82F6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.3)',
                    'fill' => true,
                    'tension' => 0.3,
                    'pointRadius' => 4,
                    'pointBackgroundColor' => '#fff',
                    'pointBorderColor' => '#3B82F6',
                ]
            ]
        ];


        $sentCount = (clone $query)->count();
        $deliveredCount = (clone $query)->where('status', 'completed')->count();
        $openedCount = (clone $query)->whereNotNull('opened_at')->count();
        $clickedCount = (clone $query)->whereNotNull('clicked_at')->count();

        $metrics = [
            'sent' => LeadSequence::count(), // ⚠ This line may be a mistake
            'delivered' => LeadSequence::where('status', 'completed')->count(),
            'opened' => LeadSequence::whereNotNull('opened_at')->count(),
            'clicked' => LeadSequence::whereNotNull('clicked_at')->count(),
        ];

        $totalSent = max($sentCount, 1);

        // Get today's follow-ups from prospects
        $todaysFollowUps = Prospect::with('assignedUser')
            ->whereDate('next_follow_up_at', Carbon::today())
            ->orderBy('next_follow_up_at', 'asc')
            ->limit(10)
            ->get();

        $filters = $request->only(['range', 'startDate', 'endDate']);

        return Inertia::render('Dashboard', compact('deliveredEmails', 'leadCount', 'leadSequenceCount', 'sequenceCount', 'filters', 'emailChart', 'metrics', 'todaysFollowUps'));
    }



}
