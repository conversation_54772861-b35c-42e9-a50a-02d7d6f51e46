import{o as i,c as r,b as s,n as t}from"./app-1e83883d.js";const c=["aria-checked"],l=s("span",{class:"sr-only"},"Use setting",-1),p={__name:"SwitchButton",props:["switchValue","userId"],emits:["updateSwitchValue"],setup(n,{emit:o}){const e=n,a=()=>{o("updateSwitchValue",!e.switchValue,e.userId)};return(u,h)=>(i(),r("button",{type:"button",class:t([{"bg-gray-200":!e.switchValue,"bg-indigo-600":e.switchValue},"relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2"]),role:"switch","aria-checked":e.switchValue,onClick:a},[l,s("span",{class:t([{"translate-x-0":!e.switchValue,"translate-x-5":e.switchValue},"pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"]),"aria-hidden":"true"},null,2)],10,c))}};export{p as _};
