import{T as d,o as m,e as l,w as t,a as e,u as o,Z as c,b as a,g as p,n as f,h as u}from"./app-1e83883d.js";import{_}from"./GuestLayout-4aa62efc.js";import{_ as w}from"./InputError-b46459b8.js";import{_ as g}from"./InputLabel-e29df6ea.js";import{P as x}from"./PrimaryButton-e7d804f6.js";import{_ as h}from"./TextInput-ea9e10ee.js";import"./_plugin-vue_export-helper-c27b6911.js";const b=a("h2",{class:"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"},"Confirm Password",-1),y=a("div",{class:"mb-4 mt-2 text-sm text-gray-500"}," This is a secure area of the application. Please confirm your password before continuing. ",-1),P=["onSubmit"],C={class:"flex justify-end mt-4"},q={__name:"ConfirmPassword",setup(V){const s=d({password:""}),i=()=>{s.post(route("password.confirm"),{onFinish:()=>s.reset()})};return(v,r)=>(m(),l(_,null,{default:t(()=>[e(o(c),{title:"Confirm Password"}),b,y,a("form",{onSubmit:u(i,["prevent"])},[a("div",null,[e(g,{for:"password",value:"Password"}),e(h,{id:"password",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:o(s).password,"onUpdate:modelValue":r[0]||(r[0]=n=>o(s).password=n),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),e(w,{class:"mt-2",message:o(s).errors.password},null,8,["message"])]),a("div",C,[e(x,{class:f(["",{"opacity-25":o(s).processing}]),disabled:o(s).processing},{default:t(()=>[p(" Confirm ")]),_:1},8,["class","disabled"])])],40,P)]),_:1}))}};export{q as default};
