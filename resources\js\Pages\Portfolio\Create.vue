<script setup>
import AdminLayout from "@/Layouts/AdminLayout.vue";
import { Head, Link, useForm } from '@inertiajs/vue3';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SvgLink from '@/Components/ActionLink.vue';

const form = useForm({
    project_name: '',
    url: '',
    description: '',
    technology: '',
});

const submit = () => {
    form.post(route('portfolios.store'), {
        onSuccess: () => form.reset(),
    });
};
</script>

<template>
    <Head title="Portfolio" />

    <AdminLayout>
        <div class="items-start">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Add New Portfolio Project</h1>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <form @submit.prevent="submit" class="space-y-6">
                    <!-- Project Name -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <InputLabel for="project_name" value="Project Name *" />
                        <TextInput
                            id="project_name"
                            v-model="form.project_name"
                            type="text"
                            class="mt-1 block w-full"
                            required
                            autofocus
                            placeholder="Enter project name"
                        />
                        <InputError class="mt-2" :message="form.errors.project_name" />
                    </div>

                    <!-- URL -->
                    <div>
                        <InputLabel for="url" value="Project URL" />
                        <TextInput
                            id="url"
                            v-model="form.url"
                            type="url"
                            class="mt-1 block w-full"
                            placeholder="https://example.com"
                        />
                        <InputError class="mt-2" :message="form.errors.url" />
                    </div>

                    <!-- Description -->
                    <div>
                        <InputLabel for="description" value="Description" />
                        <textarea
                            id="description"
                            v-model="form.description"
                            rows="3"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            placeholder="Describe your project..."
                        ></textarea>
                        <InputError class="mt-2" :message="form.errors.description" />
                    </div>

                    <!-- Technology -->
                    <div>
                        <InputLabel for="technology" value="Technologies Used" />
                        <textarea
                            id="technology"
                            v-model="form.technology"
                            rows="3"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            placeholder="e.g., Laravel, Vue.js, MySQL, Tailwind CSS"
                        ></textarea>
                        <InputError class="mt-2" :message="form.errors.technology" />
                    </div>
                    </div>
                    <!-- Submit Buttons -->
                    <div class="flex mt-6 items-center justify-between">
                        <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('portfolios.index')">
                                <template #svg>
                                    <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Back</button>
                                </template>
                        </SvgLink>
                        <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </AdminLayout>
</template>
