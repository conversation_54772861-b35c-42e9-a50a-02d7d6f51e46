import{_ as o}from"./AdminLayout-d8381eea.js";import i from"./DeleteUserForm-97b76c43.js";import m from"./UpdatePasswordForm-355ff479.js";import r from"./UpdateProfileInformationForm-639b4af0.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-1e83883d.js";import"./DangerButton-b76e5036.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-b46459b8.js";import"./InputLabel-e29df6ea.js";import"./Modal-a3dbb6fa.js";/* empty css                                                              */import"./SecondaryButton-5b714cdd.js";import"./TextInput-ea9e10ee.js";import"./PrimaryButton-e7d804f6.js";import"./TextArea-d6832033.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
