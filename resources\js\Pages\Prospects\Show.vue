<script setup>
import { ref, defineProps } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import SvgLink from '@/Components/ActionLink.vue';
import Modal from '@/Components/Modal.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import { Head, useForm } from '@inertiajs/vue3';

const props = defineProps({
  prospect: Object,
});

const showActivityModal = ref(false);
const showStatusModal = ref(false);
const showFollowUpModal = ref(false);

const activityForm = useForm({
  activity_type: 'note_added',
  title: '',
  description: '',
  activity_date: new Date().toISOString().slice(0, 16),
});

const statusForm = useForm({
  status: props.prospect.status,
  notes: '',
});

const followUpForm = useForm({
  next_follow_up_at: '',
  notes: '',
});

const convertForm = useForm({});

const addActivity = () => {
  activityForm.post(route('prospects.addActivity', props.prospect.id), {
    onSuccess: () => {
      showActivityModal.value = false;
      activityForm.reset();
    },
  });
};

const updateStatus = () => {
  statusForm.patch(route('prospects.updateStatus', props.prospect.id), {
    onSuccess: () => {
      showStatusModal.value = false;
      statusForm.reset();
    },
  });
};

const scheduleFollowUp = () => {
  followUpForm.post(route('prospects.scheduleFollowUp', props.prospect.id), {
    onSuccess: () => {
      showFollowUpModal.value = false;
      followUpForm.reset();
    },
  });
};

const convertToLead = () => {
  convertForm.post(route('prospects.convert', props.prospect.id));
};

const getStatusBadgeClass = (status) => {
  const classes = {
    'new': 'bg-blue-100 text-blue-800',
    'contacted': 'bg-yellow-100 text-yellow-800',
    'qualified': 'bg-green-100 text-green-800',
    'unqualified': 'bg-red-100 text-red-800',
    'converted': 'bg-purple-100 text-purple-800',
    'lost': 'bg-gray-100 text-gray-800',
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

const getPriorityBadgeClass = (priority) => {
  const classes = {
    'low': 'bg-gray-100 text-gray-800',
    'medium': 'bg-blue-100 text-blue-800',
    'high': 'bg-orange-100 text-orange-800',
    'urgent': 'bg-red-100 text-red-800',
  };
  return classes[priority] || 'bg-gray-100 text-gray-800';
};

const formatDate = (date) => {
  if (!date) return '-';
  return new Date(date).toLocaleString();
};

const formatCurrency = (amount) => {
  if (!amount) return '-';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const formatLeadSource = (source) => {
  return source.charAt(0).toUpperCase() + source.slice(1).replace('_', ' ');
};

const activityTypes = [
  { value: 'email_sent', label: 'Email Sent' },
  { value: 'email_received', label: 'Email Received' },
  { value: 'call_made', label: 'Call Made' },
  { value: 'call_received', label: 'Call Received' },
  { value: 'meeting_scheduled', label: 'Meeting Scheduled' },
  { value: 'meeting_completed', label: 'Meeting Completed' },
  { value: 'note_added', label: 'Note Added' },
  { value: 'linkedin_message', label: 'LinkedIn Message' },
  { value: 'proposal_sent', label: 'Proposal Sent' },
  { value: 'follow_up_scheduled', label: 'Follow-up Scheduled' },
  { value: 'document_shared', label: 'Document Shared' },
  { value: 'other', label: 'Other' },
];

const statusOptions = ['new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost'];
</script>

<template>
  <Head :title="`Prospect: ${prospect.first_name} ${prospect.last_name}`" />

  <AdminLayout>
    <div class="animate-top">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Prospect: {{ prospect.first_name }} {{ prospect.last_name }}</h1>
                <p class="text-gray-600 mt-1">Prospect Details</p>
            </div>
            <div class="flex space-x-4">
                <SvgLink :href="route('prospects.index')">
                        <template #svg>
                            <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Back</button>
                        </template>
                </SvgLink>
            </div>
        </div>
        <div class="grid grid-cols-1 lg:grid-cols-9 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-6 space-y-6">

                <!-- Basic Information -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Basic Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-semibold text-gray-900">Client Name</label>
                            <p class="mt-1 text-sm text-gray-700">{{ prospect.first_name }} {{ prospect.last_name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-900">Email</label>
                            <p class="mt-1 text-sm text-gray-700">{{ prospect.email || 'N/A' }}</p>
                        </div>
                        <div v-if="prospect.phone">
                            <label class="block text-sm font-semibold text-gray-900">Phone</label>
                            <p class="mt-1 text-sm text-gray-700">{{ prospect.phone }}</p>
                        </div>
                        <div v-if="prospect.company" >
                            <label class="block text-sm font-semibold text-gray-900">Company</label>
                            <p class="mt-1 text-sm text-gray-700">{{ prospect.company}}</p>
                        </div>
                        <div v-if="prospect.position">
                            <label class="block text-sm font-semibold text-gray-900">Position</label>
                            <p class="mt-1 text-sm text-gray-700">{{ prospect.position ?? 'N/A' }}</p>
                        </div>
                        <div v-if="prospect.country || prospect.city">
                            <label class="block text-sm font-semibold text-gray-900">Location</label>
                            <p class="mt-1 text-sm text-gray-700">{{ prospect.city }}{{ prospect.city && prospect.country ? ', ' : '' }}{{ prospect.country }}</p>
                        </div>
                    </div>
                </div>
                <!-- Timeline -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-6">Activity Timeline</h3>
                    <div v-if="prospect.activities && prospect.activities.length > 0" class="space-y-4">
                        <div v-for="activity in prospect.activities" :key="activity.id" class="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
                            <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                <span class="text-indigo-600 text-xs font-medium">
                                {{ activity.activity_type.charAt(0).toUpperCase() }}
                                </span>
                            </div>
                            </div>
                            <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-semibold text-gray-900">{{ activity.title }}</p>
                            </div>
                            <p v-if="activity.description" class="mt-1 text-sm text-gray-500">{{ activity.description }}</p>
                            <div class="flex items-center justify-between">
                                <p v-if="activity.user" class="mt-1 text-xs text-gray-600">by {{ activity.user.first_name }}</p>
                                <p class="text-xs text-gray-600">{{ formatDate(activity.activity_date) }}</p>
                            </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class="text-center py-8 text-gray-500">
                        No activities recorded yet.
                    </div>
                </div>
            </div>

            <!-- Sidebar -->

            <div class="lg:col-span-3 space-y-6">
                <!-- Quick Actions -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h3>
                    <div class="flex flex-wrap gap-3">
                        <PrimaryButton @click="showActivityModal = true">
                            Add Activity
                        </PrimaryButton>
                        <SecondaryButton class="w-full" @click="showStatusModal = true">
                            Update Status
                        </SecondaryButton>
                        <SecondaryButton class="w-full" @click="showFollowUpModal = true">
                            Schedule Follow-up
                        </SecondaryButton>
                    </div>
                </div>


                <!-- Notes -->
                <div v-if="prospect.initial_conversation || prospect.notes || prospect.requirements" class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-6">Notes & Conversation</h3>
                    <div class="space-y-4">
                        <div v-if="prospect.initial_conversation">
                            <label class="block text-sm font-semibold text-gray-900">Initial Conversation:</label>
                            <p class="mt-1 text-sm text-gray-700">{{ prospect.initial_conversation }}</p>
                            </div>
                            <div v-if="prospect.requirements">
                            <label class="block text-sm font-semibold text-gray-900">Requirements:</label>
                            <p class="mt-1 text-sm text-gray-700">{{ prospect.requirements }}</p>
                            </div>
                            <div v-if="prospect.notes">
                            <label class="block text-sm font-semibold text-gray-900">Notes:</label>
                            <p class="mt-1 text-sm text-gray-700">{{ prospect.notes }}</p>
                        </div>
                    </div>
                </div>

                <!-- URLs -->
                <div v-if="prospect.linkedin_url || prospect.website_url || prospect.company_website" class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-6">URLs</h3>
                    <div class="flex flex-col gap-1">
                        <a v-if="prospect.linkedin_url" :href="prospect.linkedin_url" target="_blank" class="text-blue-600 font-semibold hover:text-blue-800">
                        LinkedIn Profile
                        </a>
                        <a v-if="prospect.website_url" :href="prospect.website_url" target="_blank" class="text-blue-600 font-semibold hover:text-blue-800">
                        Personal Website
                        </a>
                        <a v-if="prospect.company_website" :href="prospect.company_website" target="_blank" class="text-blue-600 font-semibold hover:text-blue-800">
                        Company Website
                        </a>
                    </div>
                </div>
                <!-- Status & Priority -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-6">Status & Priority</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="">
                            <label class="block text-sm font-semibold text-gray-900">Status:</label>
                            <span :class="getStatusBadgeClass(prospect.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                            {{ prospect.status.charAt(0).toUpperCase() + prospect.status.slice(1) }}
                            </span>
                        </div>
                        <div class="">
                            <label class="block text-sm font-semibold text-gray-900">Priority:</label>
                            <span :class="getPriorityBadgeClass(prospect.priority)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                            {{ prospect.priority.charAt(0).toUpperCase() + prospect.priority.slice(1) }}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-900">Score:</label>
                            <span class="mt-1 text-sm text-gray-700">{{ prospect.score }}/100</span>
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-900">Lead Source:</label>
                            <span class="mt-1 text-sm text-gray-700">{{ formatLeadSource(prospect.lead_source) }}</span>
                        </div>
                        <div v-if="prospect.assigned_user">
                            <label class="block text-sm font-semibold text-gray-900">Assigned To:</label>
                            <span class="mt-1 text-sm text-gray-700">{{ prospect.assigned_user.name }}</span>
                        </div>
                    </div>
                </div>
                <!-- Product Specifications -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Project Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div v-if="prospect.project_type">
                            <label class="block text-sm font-semibold text-gray-900">Project Type:</label>
                            <p class="mt-1 text-sm text-gray-700">{{ prospect.project_type }}</p>
                        </div>
                        <div v-if="prospect.estimated_budget">
                            <label class="block text-sm font-semibold text-gray-900">Estimated Budget:</label>
                            <p class="mt-1 text-sm text-gray-700">{{ formatCurrency(prospect.estimated_budget) }}</p>
                        </div>
                        <div v-if="prospect.next_follow_up_at">
                            <label class="block text-sm font-semibold text-gray-900">Next Follow-up:</label>
                            <p class="mt-1 text-sm text-gray-700">{{ formatDate(prospect.next_follow_up_at) }}</p>
                        </div>
                        <div v-if="prospect.converted_lead">
                            <label class="block text-sm font-semibold text-gray-900">Converted Lead:</label>
                            <ActionLink :href="route('leads.show', prospect.converted_lead.id)" class="ml-2 text-sm text-indigo-600">
                            View Lead #{{ prospect.converted_lead.id }}
                            </ActionLink>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Add Activity Modal -->
    <Modal :show="showActivityModal" @close="showActivityModal = false">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Add Activity</h2>
        <form @submit.prevent="addActivity" class="space-y-4">
          <div>
            <InputLabel for="activity_type" value="Activity Type" />
            <select
              id="activity_type"
              v-model="activityForm.activity_type"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option v-for="type in activityTypes" :key="type.value" :value="type.value">
                {{ type.label }}
              </option>
            </select>
          </div>
          <div>
            <InputLabel for="activity_title" value="Title" />
            <TextInput
              id="activity_title"
              v-model="activityForm.title"
              type="text"
              class="mt-1 block w-full"
              required
            />
          </div>
          <div>
            <InputLabel for="activity_description" value="Description" />
            <textarea
              id="activity_description"
              v-model="activityForm.description"
              rows="3"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            ></textarea>
          </div>
          <div>
            <InputLabel for="activity_date" value="Activity Date" />
            <TextInput
              id="activity_date"
              v-model="activityForm.activity_date"
              type="datetime-local"
              class="mt-1 block w-full"
            />
          </div>
          <div class="flex justify-end space-x-3">
            <SecondaryButton @click="showActivityModal = false">Cancel</SecondaryButton>
            <PrimaryButton :disabled="activityForm.processing">Add Activity</PrimaryButton>
          </div>
        </form>
      </div>
    </Modal>

    <!-- Update Status Modal -->
    <Modal :show="showStatusModal" @close="showStatusModal = false">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Update Status</h2>
        <form @submit.prevent="updateStatus" class="space-y-4">
          <div>
            <InputLabel for="status" value="Status" />
            <select
              id="status"
              v-model="statusForm.status"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option v-for="status in statusOptions" :key="status" :value="status">
                {{ status.charAt(0).toUpperCase() + status.slice(1) }}
              </option>
            </select>
          </div>
          <div>
            <InputLabel for="status_notes" value="Notes" />
            <textarea
              id="status_notes"
              v-model="statusForm.notes"
              rows="3"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              placeholder="Optional notes about the status change"
            ></textarea>
          </div>
          <div class="flex justify-end space-x-3">
            <SecondaryButton @click="showStatusModal = false">Cancel</SecondaryButton>
            <PrimaryButton :disabled="statusForm.processing">Update Status</PrimaryButton>
          </div>
        </form>
      </div>
    </Modal>

    <!-- Schedule Follow-up Modal -->
    <Modal :show="showFollowUpModal" @close="showFollowUpModal = false">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Schedule Follow-up</h2>
        <form @submit.prevent="scheduleFollowUp" class="space-y-4">
          <div>
            <InputLabel for="follow_up_date" value="Follow-up Date" />
            <TextInput
              id="follow_up_date"
              v-model="followUpForm.next_follow_up_at"
              type="datetime-local"
              class="mt-1 block w-full"
              required
            />
          </div>
          <div>
            <InputLabel for="follow_up_notes" value="Notes" />
            <textarea
              id="follow_up_notes"
              v-model="followUpForm.notes"
              rows="3"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              placeholder="Notes about the follow-up"
            ></textarea>
          </div>
          <div class="flex justify-end space-x-3">
            <SecondaryButton @click="showFollowUpModal = false">Cancel</SecondaryButton>
            <PrimaryButton :disabled="followUpForm.processing">Schedule Follow-up</PrimaryButton>
          </div>
        </form>
      </div>
    </Modal>
  </AdminLayout>
</template>
